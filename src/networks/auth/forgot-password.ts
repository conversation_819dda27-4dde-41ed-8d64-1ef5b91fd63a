import { apiCall } from "@/lib/api"
import { SendOTPForPasswordResetBodyI } from "./types";

export const sendOTPForPasswordResetAPI = async (payload: SendOTPForPasswordResetBodyI) => {
    const result = await apiCall<SendOTPForPasswordResetBodyI, undefined>(
        '/backend/api/v1/auth/forgot-password/send-otp',
        'POST',
        {
            isAuth: false,
            payload,
        },
    );

    return result;
};
'use client';
import React, { useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import QuestionCard from '../QuestionCard';
import { QuestionCardSkeleton } from '../Shimmer';
import useForumQuestions from './useHook';
import useAuth from '@/hooks/useAuth';

const ForumFeed = () => {
  const router = useRouter();
  const {
    questions,
    loading,
    loadingMore,
    error,
    hasMoreQuestions,
    loadMore,
    retry,
    refresh,
  } = useForumQuestions();

  const { isAuthenticated } = useAuth();

  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Check if error is related to authorization
  const isAuthError = error
    ?.toLowerCase()
    .includes('authorization token is mandatory');

  // Handle login navigation
  const handleLogin = () => {
    router.push('/login');
  };

  const handleIntersection = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      if (
        entry.isIntersecting &&
        hasMoreQuestions &&
        !loadingMore &&
        !loading
      ) {
        loadMore();
      }
    },
    [hasMoreQuestions, loadingMore, loading, loadMore]
  );

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      root: null,
      rootMargin: '100px',
      threshold: 0.1,
    });

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [handleIntersection]);

  // Handle question interactions
  const handleAnswer = useCallback((questionId: string) => {
    console.log('Answer question:', questionId);
    // TODO: Implement answer functionality
  }, []);

  const handleFollow = useCallback((questionId: string) => {
    console.log('Follow question:', questionId);
    // TODO: Implement follow functionality
  }, []);

  const handlePass = useCallback((questionId: string) => {
    console.log('Pass question:', questionId);
    // TODO: Implement pass functionality
  }, []);

  // Show initial loading state
  if (loading && questions.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <QuestionCardSkeleton key={`initial-loading-${index}`} />
        ))}
      </div>
    );
  }

  // Show error state for initial load
  if (error && questions.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="text-red-500 mb-4">
            <svg
              className="w-12 h-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Failed to load questions
          </h3>
          <p className="text-sm text-gray-600 mb-4">{error}</p>

          {/* Show both Try Again and Login buttons for auth errors */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={retry}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#448600] hover:bg-[#357000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600]"
            >
              Try Again
            </button>

            {isAuthError && (
              <button
                onClick={handleLogin}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600]"
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                  />
                </svg>
                Login
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {questions.length > 0 && !loading && (
        <div className="text-center pb-2">
          <button
            onClick={refresh}
            className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-gray-900 transition-colors"
            title="Refresh feed"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Refresh
          </button>
        </div>
      )}

      {questions.map(question => (
        <QuestionCard
          key={question.id}
          question={question}
          onAnswer={handleAnswer}
          onFollow={handleFollow}
          onPass={handlePass}
          isAuthenticated={isAuthenticated}
        />
      ))}

      {/* Load More Trigger (Intersection Observer Target) */}
      {hasMoreQuestions && (
        <div ref={loadMoreRef} className="py-4">
          {loadingMore && (
            <div className="space-y-4">
              {Array.from({ length: 2 }).map((_, index) => (
                <QuestionCardSkeleton key={`loading-more-${index}`} />
              ))}
            </div>
          )}

          {/* Manual Load More Button (fallback) */}
          {!loadingMore && (
            <div className="text-center">
              <button
                onClick={loadMore}
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors"
              >
                Load More Questions
              </button>
            </div>
          )}
        </div>
      )}

      {/* End of Feed Message */}
      {!hasMoreQuestions && questions.length > 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            <svg
              className="w-8 h-8 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <p className="text-sm text-gray-500">
            You've reached the end of the feed
          </p>
        </div>
      )}

      {/* Error State for Load More */}
      {error && questions.length > 0 && (
        <div className="text-center py-6">
          <p className="text-sm text-red-600 mb-3">{error}</p>
          <button
            onClick={retry}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors"
          >
            Try Again
          </button>
        </div>
      )}
    </>
  );
};

export default ForumFeed;
